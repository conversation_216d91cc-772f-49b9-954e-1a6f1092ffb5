[ 2025-08-27T15:58:27+08:00 ][ error ] 收到消息：dz/pi/getstatus/110002
error
[ 2025-08-27T15:58:27+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/110002',
  'message' => '{"VER":"666","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1221","CH1":"1221","CH2":"1700","LS":"0","SS":"0","BS":"11.6","RSSI":27,"MT":"2","NG":"0"}',
  'timestamp' => '2025-08-27 15:58:27',
)
error
[ 2025-08-27T15:58:27+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T15:58:27+08:00 ][ error ] array (
  'VER' => '666',
  'CMD' => '12',
  'CD' => '110002',
  'SIMID' => '898604E0092220769724',
  'CS' => '0',
  'DT' => '1221',
  'CH1' => '1221',
  'CH2' => '1700',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.6',
  'RSSI' => 27,
  'MT' => '2',
  'NG' => '0',
)
error
[ 2025-08-27T15:58:27+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/110002
error
[ 2025-08-27T15:58:27+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 【状态上报】开始处理地锁 '110002' 的通用状态...
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 【状态上报】成功更新地锁 '110002' 的快照状态到数据库
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 【状态上报】成功记录地锁 '110002' 的状态到日志表
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 【业务触发】检查 '110002' 的状态变化...
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T15:58:27+08:00 ][ log ] array (
  'id' => 40,
  'sn' => '110002',
  'mainname' => '110002 测试四轮地锁',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 1,
  'voltage' => '11.60',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 2,
  'last_online_time' => 1756257158,
  'last_status_updatetime' => 1756281404,
  'createtime' => 1756194042,
  'updatetime' => 1756281404,
  'hardware_type' => 2,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '110002',
  'qrcode' => 'uploads/ewm/batch/final_qr_110002.png',
  'kehu_id' => 2,
  'lat' => '28.8517',
  'lng' => '112.9254',
  'address' => '湖南省岳阳市汨罗市岳阳市求实饲料有限公司东北(尚磊路东)',
)
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 0
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 【业务触发】检测到地锁 '110002' 车辆离开，尝试自动结束订单。
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 打印四轮地锁order信息
log
[ 2025-08-27T15:58:27+08:00 ][ log ] array (
  'id' => 213,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.20',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 40,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
  'sn' => 'ord2025082709351970903265',
  'user_id' => 5611,
  'money' => '1.40',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '3',
  'pay_time' => NULL,
  'createtime' => 1756258519,
  'returntime' => 1756281160,
  'updatetime' => 1756258519,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 7,
  'timelong_fenzhong' => 378,
  'really_money' => '1.40',
  'actreturntime' => 1756281160,
  'answer_return_time' => 1756306800,
  'overtime' => 0,
  'overtime_money' => '0.00',
  'normal_money' => '0.00',
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
  'deposit_deducted' => '0.00',
  'balance_deducted' => '0.00',
  'remaining_amount' => '1.40',
  'payment_detail' => '',
  'setmeal_deducted' => 0,
  'setmeal_log_id' => 0,
)
log
[ 2025-08-27T15:58:27+08:00 ][ log ] 打印orderEnd1结果
log
[ 2025-08-27T15:58:27+08:00 ][ log ] array (
  'success' => true,
  'msg' => '',
  'data' => 
  array (
    'id' => 213,
    'platform_id' => 20,
    'agent_id' => 82,
    'hospital_id' => 48,
    'hospital_fcbl' => 20.0,
    'hospital_price' => '0.20',
    'hospital_hourlong' => 1,
    'hospital_freedt' => 999999,
    'departments_id' => 34,
    'equipment_id' => 40,
    'equipment_info_id' => 0,
    'info' => '总平台-深圳代理商-南山物业-物业北车棚-110002 测试四轮地锁-110002',
    'sn' => 'ord2025082709351970903265',
    'user_id' => 5611,
    'money' => '1.40',
    'status' => '2',
    'pay_types' => '0',
    'pay_status' => '3',
    'pay_time' => NULL,
    'createtime' => 1756258519,
    'returntime' => 1756281507,
    'updatetime' => 1756258519,
    'is_branch' => '1',
    'admin_info' => NULL,
    'timelong' => 7,
    'timelong_fenzhong' => 384,
    'really_money' => '1.40',
    'actreturntime' => 1756281507,
    'answer_return_time' => 1756306800,
    'overtime' => 0,
    'overtime_money' => '0.00',
    'normal_money' => '0.00',
    'charging_rule' => 1,
    'use_status' => 2,
    'nb_goodsid' => '',
    'deposit_deducted' => '0.00',
    'balance_deducted' => '0.00',
    'remaining_amount' => '1.40',
    'payment_detail' => '',
    'setmeal_deducted' => 0,
    'setmeal_log_id' => 0,
  ),
)
log
[ 2025-08-27T15:58:27+08:00 ][ error ] 消息处理失败: Undefined index: code
error
[ 2025-08-27T15:59:00+08:00 ][ error ] 收到消息：dz/pi/getstatus/710017
error
[ 2025-08-27T15:59:00+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710017',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710017","SIMID":"898604F4152391140805","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":17,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 15:59:00',
)
error
[ 2025-08-27T15:59:00+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T15:59:00+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710017',
  'SIMID' => '898604F4152391140805',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 17,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T15:59:00+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710017
error
[ 2025-08-27T15:59:00+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T15:59:00+08:00 ][ log ] 【状态上报】开始处理地锁 '710017' 的通用状态...
log
[ 2025-08-27T15:59:00+08:00 ][ log ] 【状态上报】成功更新地锁 '710017' 的快照状态到数据库
log
[ 2025-08-27T15:59:00+08:00 ][ log ] 【状态上报】成功记录地锁 '710017' 的状态到日志表
log
[ 2025-08-27T15:59:00+08:00 ][ log ] 【业务触发】检查 '710017' 的状态变化...
log
[ 2025-08-27T15:59:00+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T15:59:00+08:00 ][ log ] array (
  'id' => 18,
  'sn' => '710017',
  'mainname' => '710017',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 17,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755505776,
  'last_status_updatetime' => 1756280940,
  'createtime' => NULL,
  'updatetime' => 1756280940,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T15:59:00+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T15:59:00+08:00 ][ log ] 0
log
[ 2025-08-27T15:59:25+08:00 ][ error ] 收到消息：dz/pi/getstatus/710007
error
[ 2025-08-27T15:59:25+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710007',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710007","SIMID":"898604F4152391140829","CS":"0","LS":"0","SS":"0","BS":"12.4","RSSI":22,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 15:59:25',
)
error
[ 2025-08-27T15:59:25+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T15:59:25+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710007',
  'SIMID' => '898604F4152391140829',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.4',
  'RSSI' => 22,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T15:59:25+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710007
error
[ 2025-08-27T15:59:25+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T15:59:25+08:00 ][ log ] 【状态上报】开始处理地锁 '710007' 的通用状态...
log
[ 2025-08-27T15:59:25+08:00 ][ log ] 【状态上报】成功更新地锁 '710007' 的快照状态到数据库
log
[ 2025-08-27T15:59:25+08:00 ][ log ] 【状态上报】成功记录地锁 '710007' 的状态到日志表
log
[ 2025-08-27T15:59:25+08:00 ][ log ] 【业务触发】检查 '710007' 的状态变化...
log
[ 2025-08-27T15:59:25+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T15:59:25+08:00 ][ log ] array (
  'id' => 8,
  'sn' => '710007',
  'mainname' => '710007',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.40',
  'signal_strength' => 22,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756280967,
  'createtime' => 1754624033,
  'updatetime' => 1756280967,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '700001',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T15:59:25+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T15:59:25+08:00 ][ log ] 0
log
[ 2025-08-27T15:59:27+08:00 ][ error ] 收到消息：dz/pi/getstatus/710011
error
[ 2025-08-27T15:59:27+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710011',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710011","SIMID":"898604F4152391140832","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":22,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 15:59:27',
)
error
[ 2025-08-27T15:59:27+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T15:59:27+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710011',
  'SIMID' => '898604F4152391140832',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 22,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T15:59:27+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710011
error
[ 2025-08-27T15:59:27+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T15:59:27+08:00 ][ log ] 【状态上报】开始处理地锁 '710011' 的通用状态...
log
[ 2025-08-27T15:59:27+08:00 ][ log ] 【状态上报】成功更新地锁 '710011' 的快照状态到数据库
log
[ 2025-08-27T15:59:27+08:00 ][ log ] 【状态上报】成功记录地锁 '710011' 的状态到日志表
log
[ 2025-08-27T15:59:27+08:00 ][ log ] 【业务触发】检查 '710011' 的状态变化...
log
[ 2025-08-27T15:59:27+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T15:59:27+08:00 ][ log ] array (
  'id' => 12,
  'sn' => '710011',
  'mainname' => '710011',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 22,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1756221191,
  'last_status_updatetime' => 1756280967,
  'createtime' => NULL,
  'updatetime' => 1756280967,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T15:59:27+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T15:59:27+08:00 ][ log ] 0
log
[ 2025-08-27T16:00:07+08:00 ][ error ] 收到消息：dz/pi/getstatus/710010
error
[ 2025-08-27T16:00:07+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710010',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710010","SIMID":"898604F4152391140831","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:00:07',
)
error
[ 2025-08-27T16:00:07+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:00:07+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710010',
  'SIMID' => '898604F4152391140831',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:00:07+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710010
error
[ 2025-08-27T16:00:07+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:00:07+08:00 ][ log ] 【状态上报】开始处理地锁 '710010' 的通用状态...
log
[ 2025-08-27T16:00:07+08:00 ][ log ] 【状态上报】成功更新地锁 '710010' 的快照状态到数据库
log
[ 2025-08-27T16:00:07+08:00 ][ log ] 【状态上报】成功记录地锁 '710010' 的状态到日志表
log
[ 2025-08-27T16:00:07+08:00 ][ log ] 【业务触发】检查 '710010' 的状态变化...
log
[ 2025-08-27T16:00:07+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:00:07+08:00 ][ log ] array (
  'id' => 11,
  'sn' => '710010',
  'mainname' => '710010',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.40',
  'signal_strength' => 23,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281007,
  'createtime' => NULL,
  'updatetime' => 1756281007,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:00:07+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:00:07+08:00 ][ log ] 0
log
[ 2025-08-27T16:00:14+08:00 ][ error ] 收到消息：dz/pi/getstatus/110002
error
[ 2025-08-27T16:00:14+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/110002',
  'message' => '{"VER":"V1.231111","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1155","CH1":"1155","CH2":"1700","LS":"1","SS":"0","BS":"11.8","RSSI":27,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:00:14',
)
error
[ 2025-08-27T16:00:14+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:00:14+08:00 ][ error ] array (
  'VER' => 'V1.231111',
  'CMD' => '12',
  'CD' => '110002',
  'SIMID' => '898604E0092220769724',
  'CS' => '0',
  'DT' => '1155',
  'CH1' => '1155',
  'CH2' => '1700',
  'LS' => '1',
  'SS' => '0',
  'BS' => '11.8',
  'RSSI' => 27,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:00:14+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/110002
error
[ 2025-08-27T16:00:14+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:00:14+08:00 ][ log ] 【状态上报】开始处理地锁 '110002' 的通用状态...
log
[ 2025-08-27T16:00:14+08:00 ][ log ] 【状态上报】成功更新地锁 '110002' 的快照状态到数据库
log
[ 2025-08-27T16:00:14+08:00 ][ log ] 【状态上报】成功记录地锁 '110002' 的状态到日志表
log
[ 2025-08-27T16:00:14+08:00 ][ log ] 【业务触发】检查 '110002' 的状态变化...
log
[ 2025-08-27T16:00:14+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:00:14+08:00 ][ log ] array (
  'id' => 40,
  'sn' => '110002',
  'mainname' => '110002 测试四轮地锁',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '11.60',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 2,
  'last_online_time' => 1756257158,
  'last_status_updatetime' => 1756281507,
  'createtime' => 1756194042,
  'updatetime' => 1756281507,
  'hardware_type' => 2,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '110002',
  'qrcode' => 'uploads/ewm/batch/final_qr_110002.png',
  'kehu_id' => 2,
  'lat' => '28.8517',
  'lng' => '112.9254',
  'address' => '湖南省岳阳市汨罗市岳阳市求实饲料有限公司东北(尚磊路东)',
)
log
[ 2025-08-27T16:00:14+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:00:14+08:00 ][ log ] 0
log
[ 2025-08-27T16:01:17+08:00 ][ error ] 收到消息：dz/pi/getstatus/710020
error
[ 2025-08-27T16:01:17+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710020',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710020","SIMID":"898604F4152391140824","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":24,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:01:17',
)
error
[ 2025-08-27T16:01:17+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:01:17+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710020',
  'SIMID' => '898604F4152391140824',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 24,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:01:17+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710020
error
[ 2025-08-27T16:01:17+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:01:17+08:00 ][ log ] 【状态上报】开始处理地锁 '710020' 的通用状态...
log
[ 2025-08-27T16:01:17+08:00 ][ log ] 【状态上报】成功更新地锁 '710020' 的快照状态到数据库
log
[ 2025-08-27T16:01:17+08:00 ][ log ] 【状态上报】成功记录地锁 '710020' 的状态到日志表
log
[ 2025-08-27T16:01:17+08:00 ][ log ] 【业务触发】检查 '710020' 的状态变化...
log
[ 2025-08-27T16:01:17+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:01:17+08:00 ][ log ] array (
  'id' => 21,
  'sn' => '710020',
  'mainname' => '710020',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 24,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281076,
  'createtime' => NULL,
  'updatetime' => 1756281076,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:01:17+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:01:17+08:00 ][ log ] 0
log
[ 2025-08-27T16:01:47+08:00 ][ error ] 收到消息：dz/pi/getstatus/710015
error
[ 2025-08-27T16:01:47+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710015',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710015","SIMID":"898604F4152391140836","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":24,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:01:47',
)
error
[ 2025-08-27T16:01:47+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:01:47+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710015',
  'SIMID' => '898604F4152391140836',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 24,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:01:47+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710015
error
[ 2025-08-27T16:01:47+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:01:47+08:00 ][ log ] 【状态上报】开始处理地锁 '710015' 的通用状态...
log
[ 2025-08-27T16:01:47+08:00 ][ log ] 【状态上报】成功更新地锁 '710015' 的快照状态到数据库
log
[ 2025-08-27T16:01:47+08:00 ][ log ] 【状态上报】成功记录地锁 '710015' 的状态到日志表
log
[ 2025-08-27T16:01:47+08:00 ][ log ] 【业务触发】检查 '710015' 的状态变化...
log
[ 2025-08-27T16:01:47+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:01:47+08:00 ][ log ] array (
  'id' => 16,
  'sn' => '710015',
  'mainname' => '710015',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 24,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281108,
  'createtime' => NULL,
  'updatetime' => 1756281108,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:01:47+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:01:47+08:00 ][ log ] 0
log
[ 2025-08-27T16:02:17+08:00 ][ error ] 收到消息：dz/pi/getstatus/710009
error
[ 2025-08-27T16:02:17+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710009',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710009","SIMID":"898604F4152391140830","CS":"0","LS":"0","SS":"0","BS":"12.2","RSSI":21,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:02:17',
)
error
[ 2025-08-27T16:02:17+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:02:17+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710009',
  'SIMID' => '898604F4152391140830',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 21,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:02:17+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710009
error
[ 2025-08-27T16:02:17+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:02:17+08:00 ][ log ] 【状态上报】开始处理地锁 '710009' 的通用状态...
log
[ 2025-08-27T16:02:17+08:00 ][ log ] 【状态上报】成功更新地锁 '710009' 的快照状态到数据库
log
[ 2025-08-27T16:02:17+08:00 ][ log ] 【状态上报】成功记录地锁 '710009' 的状态到日志表
log
[ 2025-08-27T16:02:17+08:00 ][ log ] 【业务触发】检查 '710009' 的状态变化...
log
[ 2025-08-27T16:02:17+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:02:17+08:00 ][ log ] array (
  'id' => 10,
  'sn' => '710009',
  'mainname' => '710009',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.20',
  'signal_strength' => 21,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755506970,
  'last_status_updatetime' => 1756281137,
  'createtime' => NULL,
  'updatetime' => 1756281137,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:02:17+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:02:17+08:00 ][ log ] 0
log
[ 2025-08-27T16:02:41+08:00 ][ error ] 收到消息：dz/pi/getstatus/710013
error
[ 2025-08-27T16:02:41+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710013',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710013","SIMID":"898604F4152391140834","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":22,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:02:41',
)
error
[ 2025-08-27T16:02:41+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:02:41+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710013',
  'SIMID' => '898604F4152391140834',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 22,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:02:41+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710013
error
[ 2025-08-27T16:02:41+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:02:41+08:00 ][ log ] 【状态上报】开始处理地锁 '710013' 的通用状态...
log
[ 2025-08-27T16:02:41+08:00 ][ log ] 【状态上报】成功更新地锁 '710013' 的快照状态到数据库
log
[ 2025-08-27T16:02:41+08:00 ][ log ] 【状态上报】成功记录地锁 '710013' 的状态到日志表
log
[ 2025-08-27T16:02:41+08:00 ][ log ] 【业务触发】检查 '710013' 的状态变化...
log
[ 2025-08-27T16:02:41+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:02:41+08:00 ][ log ] array (
  'id' => 14,
  'sn' => '710013',
  'mainname' => '710013',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 22,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281162,
  'createtime' => NULL,
  'updatetime' => 1756281162,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => '36.6800',
  'lng' => '119.0900',
  'address' => '山东省潍坊市潍城区山东经贸职业学院',
)
log
[ 2025-08-27T16:02:41+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:02:41+08:00 ][ log ] 0
log
[ 2025-08-27T16:02:45+08:00 ][ error ] 收到消息：dz/pi/getstatus/710006
error
[ 2025-08-27T16:02:45+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710006',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710006","SIMID":"898604F4152391140828","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":27,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:02:45',
)
error
[ 2025-08-27T16:02:45+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:02:45+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710006',
  'SIMID' => '898604F4152391140828',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 27,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:02:45+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710006
error
[ 2025-08-27T16:02:45+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:02:45+08:00 ][ log ] 【状态上报】开始处理地锁 '710006' 的通用状态...
log
[ 2025-08-27T16:02:45+08:00 ][ log ] 【状态上报】成功更新地锁 '710006' 的快照状态到数据库
log
[ 2025-08-27T16:02:45+08:00 ][ log ] 【状态上报】成功记录地锁 '710006' 的状态到日志表
log
[ 2025-08-27T16:02:45+08:00 ][ log ] 【业务触发】检查 '710006' 的状态变化...
log
[ 2025-08-27T16:02:45+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:02:45+08:00 ][ log ] array (
  'id' => 7,
  'sn' => '710006',
  'mainname' => '710006',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281164,
  'createtime' => 1754909230,
  'updatetime' => 1756281164,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '20',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:02:45+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:02:45+08:00 ][ log ] 0
log
[ 2025-08-27T16:03:01+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-27T16:03:01+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":24,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:03:01',
)
error
[ 2025-08-27T16:03:01+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:03:01+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 24,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:03:01+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-27T16:03:01+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:03:01+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-27T16:03:01+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-27T16:03:01+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-27T16:03:01+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-27T16:03:01+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:03:01+08:00 ][ log ] array (
  'id' => 4,
  'sn' => '710003',
  'mainname' => '设备710003',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 24,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281181,
  'createtime' => 1754897273,
  'updatetime' => 1756281181,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '700003',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => '0.0000',
  'lng' => '0.0000',
  'address' => '',
)
log
[ 2025-08-27T16:03:01+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:03:01+08:00 ][ log ] 0
log
[ 2025-08-27T16:03:59+08:00 ][ error ] 收到消息：dz/pi/getstatus/710002
error
[ 2025-08-27T16:03:59+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710002',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710002","SIMID":"898604F4152391140826","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":27,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:03:59',
)
error
[ 2025-08-27T16:03:59+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:03:59+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710002',
  'SIMID' => '898604F4152391140826',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 27,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:03:59+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710002
error
[ 2025-08-27T16:03:59+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:03:59+08:00 ][ log ] 【状态上报】开始处理地锁 '710002' 的通用状态...
log
[ 2025-08-27T16:03:59+08:00 ][ log ] 【状态上报】成功更新地锁 '710002' 的快照状态到数据库
log
[ 2025-08-27T16:03:59+08:00 ][ log ] 【状态上报】成功记录地锁 '710002' 的状态到日志表
log
[ 2025-08-27T16:03:59+08:00 ][ log ] 【业务触发】检查 '710002' 的状态变化...
log
[ 2025-08-27T16:03:59+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:03:59+08:00 ][ log ] array (
  'id' => 3,
  'sn' => '710002',
  'mainname' => '710002',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281239,
  'createtime' => 1754624033,
  'updatetime' => 1756281239,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '700002',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => '0.0000',
  'lng' => '0.0000',
  'address' => '',
)
log
[ 2025-08-27T16:03:59+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:03:59+08:00 ][ log ] 0
log
[ 2025-08-27T16:05:12+08:00 ][ error ] 收到消息：dz/pi/getstatus/710004
error
[ 2025-08-27T16:05:12+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710004',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710004","SIMID":"898604F4152391140827","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":25,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:05:12',
)
error
[ 2025-08-27T16:05:12+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:05:12+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710004',
  'SIMID' => '898604F4152391140827',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 25,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:05:12+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710004
error
[ 2025-08-27T16:05:12+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:05:12+08:00 ][ log ] 【状态上报】开始处理地锁 '710004' 的通用状态...
log
[ 2025-08-27T16:05:12+08:00 ][ log ] 【状态上报】成功更新地锁 '710004' 的快照状态到数据库
log
[ 2025-08-27T16:05:12+08:00 ][ log ] 【状态上报】成功记录地锁 '710004' 的状态到日志表
log
[ 2025-08-27T16:05:12+08:00 ][ log ] 【业务触发】检查 '710004' 的状态变化...
log
[ 2025-08-27T16:05:12+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:05:12+08:00 ][ log ] array (
  'id' => 5,
  'sn' => '710004',
  'mainname' => '710004',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 25,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281311,
  'createtime' => 1754904752,
  'updatetime' => 1756281311,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '20',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:05:12+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:05:12+08:00 ][ log ] 0
log
[ 2025-08-27T16:05:21+08:00 ][ error ] 收到消息：dz/pi/mstatus/12345
error
[ 2025-08-27T16:05:21+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/12345',
  'message' => '{"MD":"12345", "MS":"0"}',
  'timestamp' => '2025-08-27 16:05:21',
)
error
[ 2025-08-27T16:05:21+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:05:21+08:00 ][ error ] array (
  'MD' => '12345',
  'MS' => '0',
)
error
[ 2025-08-27T16:05:21+08:00 ][ log ] 收到地锁 '12345' 的状态更新: 离线, 信号强度: 0
log
[ 2025-08-27T16:05:21+08:00 ][ log ] 未在数据库中找到SN为 '12345' 的地锁设备
log
[ 2025-08-27T16:05:33+08:00 ][ error ] 收到消息：dz/pi/getstatus/710019
error
[ 2025-08-27T16:05:33+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710019',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710019","SIMID":"898604F4152391140802","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":25,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:05:33',
)
error
[ 2025-08-27T16:05:33+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:05:33+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710019',
  'SIMID' => '898604F4152391140802',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 25,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:05:33+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710019
error
[ 2025-08-27T16:05:33+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:05:33+08:00 ][ log ] 【状态上报】开始处理地锁 '710019' 的通用状态...
log
[ 2025-08-27T16:05:33+08:00 ][ log ] 【状态上报】成功更新地锁 '710019' 的快照状态到数据库
log
[ 2025-08-27T16:05:33+08:00 ][ log ] 【状态上报】成功记录地锁 '710019' 的状态到日志表
log
[ 2025-08-27T16:05:33+08:00 ][ log ] 【业务触发】检查 '710019' 的状态变化...
log
[ 2025-08-27T16:05:33+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:05:33+08:00 ][ log ] array (
  'id' => 20,
  'sn' => '710019',
  'mainname' => '710019',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 25,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281334,
  'createtime' => NULL,
  'updatetime' => 1756281334,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:05:33+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:05:33+08:00 ][ log ] 0
log
[ 2025-08-27T16:06:11+08:00 ][ error ] 收到消息：dz/pi/getstatus/710018
error
[ 2025-08-27T16:06:11+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710018',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710018","SIMID":"898604F4152391140803","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":4,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:06:11',
)
error
[ 2025-08-27T16:06:11+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:06:11+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710018',
  'SIMID' => '898604F4152391140803',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 4,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:06:11+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710018
error
[ 2025-08-27T16:06:11+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【状态上报】开始处理地锁 '710018' 的通用状态...
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【状态上报】成功更新地锁 '710018' 的快照状态到数据库
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【状态上报】成功记录地锁 '710018' 的状态到日志表
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【业务触发】检查 '710018' 的状态变化...
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:06:11+08:00 ][ log ] array (
  'id' => 19,
  'sn' => '710018',
  'mainname' => '710018',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 4,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755855017,
  'last_status_updatetime' => 1756281372,
  'createtime' => NULL,
  'updatetime' => 1756281372,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 0
log
[ 2025-08-27T16:06:11+08:00 ][ error ] 收到消息：dz/pi/getstatus/710014
error
[ 2025-08-27T16:06:11+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710014',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710014","SIMID":"898604F4152391140835","CS":"0","LS":"0","SS":"0","BS":"12.4","RSSI":19,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:06:11',
)
error
[ 2025-08-27T16:06:11+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:06:11+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710014',
  'SIMID' => '898604F4152391140835',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.4',
  'RSSI' => 19,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:06:11+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710014
error
[ 2025-08-27T16:06:11+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【状态上报】开始处理地锁 '710014' 的通用状态...
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【状态上报】成功更新地锁 '710014' 的快照状态到数据库
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【状态上报】成功记录地锁 '710014' 的状态到日志表
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 【业务触发】检查 '710014' 的状态变化...
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:06:11+08:00 ][ log ] array (
  'id' => 15,
  'sn' => '710014',
  'mainname' => '710014',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.40',
  'signal_strength' => 19,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1756206086,
  'last_status_updatetime' => 1756281372,
  'createtime' => NULL,
  'updatetime' => 1756281372,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:06:11+08:00 ][ log ] 0
log
[ 2025-08-27T16:06:38+08:00 ][ error ] 收到消息：dz/pi/getstatus/710016
error
[ 2025-08-27T16:06:38+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710016',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710016","SIMID":"898604F4152391140804","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":7,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:06:38',
)
error
[ 2025-08-27T16:06:38+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:06:38+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710016',
  'SIMID' => '898604F4152391140804',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 7,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:06:38+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710016
error
[ 2025-08-27T16:06:38+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:06:38+08:00 ][ log ] 【状态上报】开始处理地锁 '710016' 的通用状态...
log
[ 2025-08-27T16:06:38+08:00 ][ log ] 【状态上报】成功更新地锁 '710016' 的快照状态到数据库
log
[ 2025-08-27T16:06:38+08:00 ][ log ] 【状态上报】成功记录地锁 '710016' 的状态到日志表
log
[ 2025-08-27T16:06:38+08:00 ][ log ] 【业务触发】检查 '710016' 的状态变化...
log
[ 2025-08-27T16:06:38+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:06:38+08:00 ][ log ] array (
  'id' => 17,
  'sn' => '710016',
  'mainname' => '710016',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 7,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755511118,
  'last_status_updatetime' => 1756281397,
  'createtime' => NULL,
  'updatetime' => 1756281397,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:06:38+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:06:38+08:00 ][ log ] 0
log
[ 2025-08-27T16:06:46+08:00 ][ error ] 收到消息：dz/pi/getstatus/710005
error
[ 2025-08-27T16:06:46+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710005',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710005","SIMID":"898604F4152391140838","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":22,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:06:46',
)
error
[ 2025-08-27T16:06:46+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:06:46+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710005',
  'SIMID' => '898604F4152391140838',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 22,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:06:46+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710005
error
[ 2025-08-27T16:06:46+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:06:46+08:00 ][ log ] 【状态上报】开始处理地锁 '710005' 的通用状态...
log
[ 2025-08-27T16:06:46+08:00 ][ log ] 【状态上报】成功更新地锁 '710005' 的快照状态到数据库
log
[ 2025-08-27T16:06:46+08:00 ][ log ] 【状态上报】成功记录地锁 '710005' 的状态到日志表
log
[ 2025-08-27T16:06:46+08:00 ][ log ] 【业务触发】检查 '710005' 的状态变化...
log
[ 2025-08-27T16:06:46+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:06:46+08:00 ][ log ] array (
  'id' => 6,
  'sn' => '710005',
  'mainname' => '710005',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.20',
  'signal_strength' => 22,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755619671,
  'last_status_updatetime' => 1756281406,
  'createtime' => 1754909554,
  'updatetime' => 1756281406,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '20',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:06:46+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:06:46+08:00 ][ log ] 0
log
[ 2025-08-27T16:06:47+08:00 ][ error ] 收到消息：dz/pi/getstatus/710012
error
[ 2025-08-27T16:06:47+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710012',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710012","SIMID":"898604F4152391140833","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":21,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:06:47',
)
error
[ 2025-08-27T16:06:47+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:06:47+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710012',
  'SIMID' => '898604F4152391140833',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 21,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:06:47+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710012
error
[ 2025-08-27T16:06:47+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:06:47+08:00 ][ log ] 【状态上报】开始处理地锁 '710012' 的通用状态...
log
[ 2025-08-27T16:06:47+08:00 ][ log ] 【状态上报】成功更新地锁 '710012' 的快照状态到数据库
log
[ 2025-08-27T16:06:47+08:00 ][ log ] 【状态上报】成功记录地锁 '710012' 的状态到日志表
log
[ 2025-08-27T16:06:47+08:00 ][ log ] 【业务触发】检查 '710012' 的状态变化...
log
[ 2025-08-27T16:06:47+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:06:47+08:00 ][ log ] array (
  'id' => 13,
  'sn' => '710012',
  'mainname' => '710012',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 21,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1756257647,
  'last_status_updatetime' => 1756281408,
  'createtime' => NULL,
  'updatetime' => 1756281408,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => '36.6824',
  'lng' => '119.0948',
  'address' => '山东省潍坊市潍城区山东经贸职业学院',
)
log
[ 2025-08-27T16:06:47+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:06:47+08:00 ][ log ] 0
log
[ 2025-08-27T16:07:09+08:00 ][ error ] 收到消息：dz/pi/getstatus/710008
error
[ 2025-08-27T16:07:09+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710008',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710008","SIMID":"898604F4152391140837","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":21,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:07:09',
)
error
[ 2025-08-27T16:07:09+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:07:09+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710008',
  'SIMID' => '898604F4152391140837',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 21,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:07:09+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710008
error
[ 2025-08-27T16:07:09+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:07:09+08:00 ][ log ] 【状态上报】开始处理地锁 '710008' 的通用状态...
log
[ 2025-08-27T16:07:09+08:00 ][ log ] 【状态上报】成功更新地锁 '710008' 的快照状态到数据库
log
[ 2025-08-27T16:07:09+08:00 ][ log ] 【状态上报】成功记录地锁 '710008' 的状态到日志表
log
[ 2025-08-27T16:07:09+08:00 ][ log ] 【业务触发】检查 '710008' 的状态变化...
log
[ 2025-08-27T16:07:09+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:07:09+08:00 ][ log ] array (
  'id' => 9,
  'sn' => '710008',
  'mainname' => '710008',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 21,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755511936,
  'last_status_updatetime' => 1756281430,
  'createtime' => 1754624033,
  'updatetime' => 1756281430,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '700001',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:07:09+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:07:09+08:00 ][ log ] 0
log
[ 2025-08-27T16:08:59+08:00 ][ error ] 收到消息：dz/pi/getstatus/710017
error
[ 2025-08-27T16:08:59+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710017',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710017","SIMID":"898604F4152391140805","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":17,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:08:59',
)
error
[ 2025-08-27T16:08:59+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:08:59+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710017',
  'SIMID' => '898604F4152391140805',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 17,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:08:59+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710017
error
[ 2025-08-27T16:08:59+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:08:59+08:00 ][ log ] 【状态上报】开始处理地锁 '710017' 的通用状态...
log
[ 2025-08-27T16:08:59+08:00 ][ log ] 【状态上报】成功更新地锁 '710017' 的快照状态到数据库
log
[ 2025-08-27T16:08:59+08:00 ][ log ] 【状态上报】成功记录地锁 '710017' 的状态到日志表
log
[ 2025-08-27T16:08:59+08:00 ][ log ] 【业务触发】检查 '710017' 的状态变化...
log
[ 2025-08-27T16:08:59+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:08:59+08:00 ][ log ] array (
  'id' => 18,
  'sn' => '710017',
  'mainname' => '710017',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 17,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755505776,
  'last_status_updatetime' => 1756281540,
  'createtime' => NULL,
  'updatetime' => 1756281540,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:08:59+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:08:59+08:00 ][ log ] 0
log
[ 2025-08-27T16:09:23+08:00 ][ error ] 收到消息：dz/pi/getstatus/710007
error
[ 2025-08-27T16:09:23+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710007',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710007","SIMID":"898604F4152391140829","CS":"0","LS":"0","SS":"0","BS":"12.4","RSSI":22,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:09:23',
)
error
[ 2025-08-27T16:09:23+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:09:23+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710007',
  'SIMID' => '898604F4152391140829',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.4',
  'RSSI' => 22,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:09:23+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710007
error
[ 2025-08-27T16:09:23+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:09:23+08:00 ][ log ] 【状态上报】开始处理地锁 '710007' 的通用状态...
log
[ 2025-08-27T16:09:23+08:00 ][ log ] 【状态上报】成功更新地锁 '710007' 的快照状态到数据库
log
[ 2025-08-27T16:09:23+08:00 ][ log ] 【状态上报】成功记录地锁 '710007' 的状态到日志表
log
[ 2025-08-27T16:09:23+08:00 ][ log ] 【业务触发】检查 '710007' 的状态变化...
log
[ 2025-08-27T16:09:23+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:09:23+08:00 ][ log ] array (
  'id' => 8,
  'sn' => '710007',
  'mainname' => '710007',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.40',
  'signal_strength' => 22,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281565,
  'createtime' => 1754624033,
  'updatetime' => 1756281565,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '700001',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:09:23+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:09:23+08:00 ][ log ] 0
log
[ 2025-08-27T16:09:26+08:00 ][ error ] 收到消息：dz/pi/getstatus/710011
error
[ 2025-08-27T16:09:26+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710011',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710011","SIMID":"898604F4152391140832","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":22,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:09:26',
)
error
[ 2025-08-27T16:09:26+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:09:26+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710011',
  'SIMID' => '898604F4152391140832',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 22,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:09:26+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710011
error
[ 2025-08-27T16:09:26+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:09:26+08:00 ][ log ] 【状态上报】开始处理地锁 '710011' 的通用状态...
log
[ 2025-08-27T16:09:26+08:00 ][ log ] 【状态上报】成功更新地锁 '710011' 的快照状态到数据库
log
[ 2025-08-27T16:09:26+08:00 ][ log ] 【状态上报】成功记录地锁 '710011' 的状态到日志表
log
[ 2025-08-27T16:09:26+08:00 ][ log ] 【业务触发】检查 '710011' 的状态变化...
log
[ 2025-08-27T16:09:26+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:09:26+08:00 ][ log ] array (
  'id' => 12,
  'sn' => '710011',
  'mainname' => '710011',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 22,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1756221191,
  'last_status_updatetime' => 1756281567,
  'createtime' => NULL,
  'updatetime' => 1756281567,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:09:26+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:09:26+08:00 ][ log ] 0
log
[ 2025-08-27T16:10:08+08:00 ][ error ] 收到消息：dz/pi/getstatus/710010
error
[ 2025-08-27T16:10:08+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710010',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710010","SIMID":"898604F4152391140831","CS":"0","LS":"0","SS":"0","BS":"12.4","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:10:08',
)
error
[ 2025-08-27T16:10:08+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:10:08+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710010',
  'SIMID' => '898604F4152391140831',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.4',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:10:08+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710010
error
[ 2025-08-27T16:10:08+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:10:08+08:00 ][ log ] 【状态上报】开始处理地锁 '710010' 的通用状态...
log
[ 2025-08-27T16:10:08+08:00 ][ log ] 【状态上报】成功更新地锁 '710010' 的快照状态到数据库
log
[ 2025-08-27T16:10:08+08:00 ][ log ] 【状态上报】成功记录地锁 '710010' 的状态到日志表
log
[ 2025-08-27T16:10:08+08:00 ][ log ] 【业务触发】检查 '710010' 的状态变化...
log
[ 2025-08-27T16:10:08+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:10:08+08:00 ][ log ] array (
  'id' => 11,
  'sn' => '710010',
  'mainname' => '710010',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 23,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281607,
  'createtime' => NULL,
  'updatetime' => 1756281607,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:10:08+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:10:08+08:00 ][ log ] 0
log
[ 2025-08-27T16:10:15+08:00 ][ error ] 收到消息：dz/pi/getstatus/110002
error
[ 2025-08-27T16:10:15+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/110002',
  'message' => '{"VER":"V1.231111","CMD":"12","CD":"110002","SIMID":"898604E0092220769724","CS":"0","DT":"1155","CH1":"1155","CH2":"1700","LS":"1","SS":"0","BS":"11.8","RSSI":27,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:10:15',
)
error
[ 2025-08-27T16:10:15+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:10:15+08:00 ][ error ] array (
  'VER' => 'V1.231111',
  'CMD' => '12',
  'CD' => '110002',
  'SIMID' => '898604E0092220769724',
  'CS' => '0',
  'DT' => '1155',
  'CH1' => '1155',
  'CH2' => '1700',
  'LS' => '1',
  'SS' => '0',
  'BS' => '11.8',
  'RSSI' => 27,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:10:15+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/110002
error
[ 2025-08-27T16:10:15+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:10:15+08:00 ][ log ] 【状态上报】开始处理地锁 '110002' 的通用状态...
log
[ 2025-08-27T16:10:15+08:00 ][ log ] 【状态上报】成功更新地锁 '110002' 的快照状态到数据库
log
[ 2025-08-27T16:10:15+08:00 ][ log ] 【状态上报】成功记录地锁 '110002' 的状态到日志表
log
[ 2025-08-27T16:10:15+08:00 ][ log ] 【业务触发】检查 '110002' 的状态变化...
log
[ 2025-08-27T16:10:15+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:10:15+08:00 ][ log ] array (
  'id' => 40,
  'sn' => '110002',
  'mainname' => '110002 测试四轮地锁',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 1,
  'car_status' => 0,
  'voltage' => '11.80',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 2,
  'last_online_time' => 1756257158,
  'last_status_updatetime' => 1756281614,
  'createtime' => 1756194042,
  'updatetime' => 1756281614,
  'hardware_type' => 2,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '110002',
  'qrcode' => 'uploads/ewm/batch/final_qr_110002.png',
  'kehu_id' => 2,
  'lat' => '28.8517',
  'lng' => '112.9254',
  'address' => '湖南省岳阳市汨罗市岳阳市求实饲料有限公司东北(尚磊路东)',
)
log
[ 2025-08-27T16:10:15+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:10:15+08:00 ][ log ] 0
log
[ 2025-08-27T16:11:18+08:00 ][ error ] 收到消息：dz/pi/getstatus/710020
error
[ 2025-08-27T16:11:18+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710020',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710020","SIMID":"898604F4152391140824","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":24,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:11:18',
)
error
[ 2025-08-27T16:11:18+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:11:18+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710020',
  'SIMID' => '898604F4152391140824',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 24,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:11:18+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710020
error
[ 2025-08-27T16:11:18+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:11:18+08:00 ][ log ] 【状态上报】开始处理地锁 '710020' 的通用状态...
log
[ 2025-08-27T16:11:18+08:00 ][ log ] 【状态上报】成功更新地锁 '710020' 的快照状态到数据库
log
[ 2025-08-27T16:11:18+08:00 ][ log ] 【状态上报】成功记录地锁 '710020' 的状态到日志表
log
[ 2025-08-27T16:11:18+08:00 ][ log ] 【业务触发】检查 '710020' 的状态变化...
log
[ 2025-08-27T16:11:18+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:11:18+08:00 ][ log ] array (
  'id' => 21,
  'sn' => '710020',
  'mainname' => '710020',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 24,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281677,
  'createtime' => NULL,
  'updatetime' => 1756281677,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:11:18+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:11:18+08:00 ][ log ] 0
log
[ 2025-08-27T16:11:46+08:00 ][ error ] 收到消息：dz/pi/getstatus/710015
error
[ 2025-08-27T16:11:46+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710015',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710015","SIMID":"898604F4152391140836","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":24,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:11:46',
)
error
[ 2025-08-27T16:11:46+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:11:46+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710015',
  'SIMID' => '898604F4152391140836',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 24,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:11:46+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710015
error
[ 2025-08-27T16:11:46+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:11:46+08:00 ][ log ] 【状态上报】开始处理地锁 '710015' 的通用状态...
log
[ 2025-08-27T16:11:46+08:00 ][ log ] 【状态上报】成功更新地锁 '710015' 的快照状态到数据库
log
[ 2025-08-27T16:11:46+08:00 ][ log ] 【状态上报】成功记录地锁 '710015' 的状态到日志表
log
[ 2025-08-27T16:11:46+08:00 ][ log ] 【业务触发】检查 '710015' 的状态变化...
log
[ 2025-08-27T16:11:46+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:11:46+08:00 ][ log ] array (
  'id' => 16,
  'sn' => '710015',
  'mainname' => '710015',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 24,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281707,
  'createtime' => NULL,
  'updatetime' => 1756281707,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:11:46+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:11:46+08:00 ][ log ] 0
log
[ 2025-08-27T16:12:18+08:00 ][ error ] 收到消息：dz/pi/getstatus/710009
error
[ 2025-08-27T16:12:18+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710009',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710009","SIMID":"898604F4152391140830","CS":"0","LS":"0","SS":"0","BS":"12.2","RSSI":21,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:12:18',
)
error
[ 2025-08-27T16:12:18+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:12:18+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710009',
  'SIMID' => '898604F4152391140830',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 21,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:12:18+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710009
error
[ 2025-08-27T16:12:18+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:12:18+08:00 ][ log ] 【状态上报】开始处理地锁 '710009' 的通用状态...
log
[ 2025-08-27T16:12:18+08:00 ][ log ] 【状态上报】成功更新地锁 '710009' 的快照状态到数据库
log
[ 2025-08-27T16:12:18+08:00 ][ log ] 【状态上报】成功记录地锁 '710009' 的状态到日志表
log
[ 2025-08-27T16:12:18+08:00 ][ log ] 【业务触发】检查 '710009' 的状态变化...
log
[ 2025-08-27T16:12:18+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:12:18+08:00 ][ log ] array (
  'id' => 10,
  'sn' => '710009',
  'mainname' => '710009',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.20',
  'signal_strength' => 21,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755506970,
  'last_status_updatetime' => 1756281737,
  'createtime' => NULL,
  'updatetime' => 1756281737,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => NULL,
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:12:18+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:12:18+08:00 ][ log ] 0
log
[ 2025-08-27T16:12:21+08:00 ][ error ] 收到消息：dz/pi/mstatus/12345
error
[ 2025-08-27T16:12:21+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/12345',
  'message' => '{"MD":"12345", "MS":"0"}',
  'timestamp' => '2025-08-27 16:12:21',
)
error
[ 2025-08-27T16:12:21+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:12:21+08:00 ][ error ] array (
  'MD' => '12345',
  'MS' => '0',
)
error
[ 2025-08-27T16:12:21+08:00 ][ log ] 收到地锁 '12345' 的状态更新: 离线, 信号强度: 0
log
[ 2025-08-27T16:12:21+08:00 ][ log ] 未在数据库中找到SN为 '12345' 的地锁设备
log
[ 2025-08-27T16:12:40+08:00 ][ error ] 收到消息：dz/pi/getstatus/710013
error
[ 2025-08-27T16:12:40+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710013',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710013","SIMID":"898604F4152391140834","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":22,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:12:40',
)
error
[ 2025-08-27T16:12:40+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:12:40+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710013',
  'SIMID' => '898604F4152391140834',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 22,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:12:40+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710013
error
[ 2025-08-27T16:12:40+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:12:40+08:00 ][ log ] 【状态上报】开始处理地锁 '710013' 的通用状态...
log
[ 2025-08-27T16:12:40+08:00 ][ log ] 【状态上报】成功更新地锁 '710013' 的快照状态到数据库
log
[ 2025-08-27T16:12:40+08:00 ][ log ] 【状态上报】成功记录地锁 '710013' 的状态到日志表
log
[ 2025-08-27T16:12:40+08:00 ][ log ] 【业务触发】检查 '710013' 的状态变化...
log
[ 2025-08-27T16:12:40+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:12:40+08:00 ][ log ] array (
  'id' => 14,
  'sn' => '710013',
  'mainname' => '710013',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 22,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281761,
  'createtime' => NULL,
  'updatetime' => 1756281761,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => '36.6800',
  'lng' => '119.0900',
  'address' => '山东省潍坊市潍城区山东经贸职业学院',
)
log
[ 2025-08-27T16:12:40+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:12:40+08:00 ][ log ] 0
log
[ 2025-08-27T16:12:45+08:00 ][ error ] 收到消息：dz/pi/getstatus/710006
error
[ 2025-08-27T16:12:45+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710006',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710006","SIMID":"898604F4152391140828","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":27,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:12:45',
)
error
[ 2025-08-27T16:12:45+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:12:45+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710006',
  'SIMID' => '898604F4152391140828',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 27,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:12:45+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710006
error
[ 2025-08-27T16:12:45+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:12:45+08:00 ][ log ] 【状态上报】开始处理地锁 '710006' 的通用状态...
log
[ 2025-08-27T16:12:45+08:00 ][ log ] 【状态上报】成功更新地锁 '710006' 的快照状态到数据库
log
[ 2025-08-27T16:12:45+08:00 ][ log ] 【状态上报】成功记录地锁 '710006' 的状态到日志表
log
[ 2025-08-27T16:12:45+08:00 ][ log ] 【业务触发】检查 '710006' 的状态变化...
log
[ 2025-08-27T16:12:45+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:12:45+08:00 ][ log ] array (
  'id' => 7,
  'sn' => '710006',
  'mainname' => '710006',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 27,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281765,
  'createtime' => 1754909230,
  'updatetime' => 1756281765,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '20',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => NULL,
  'lng' => NULL,
  'address' => NULL,
)
log
[ 2025-08-27T16:12:45+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:12:45+08:00 ][ log ] 0
log
[ 2025-08-27T16:13:01+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-27T16:13:01+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":24,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-27 16:13:01',
)
error
[ 2025-08-27T16:13:01+08:00 ][ error ] 解析数据：
error
[ 2025-08-27T16:13:01+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 24,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-27T16:13:01+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-27T16:13:01+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-27T16:13:01+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-27T16:13:01+08:00 ][ log ] 【状态上报】成功更新地锁 '710003' 的快照状态到数据库
log
[ 2025-08-27T16:13:01+08:00 ][ log ] 【状态上报】成功记录地锁 '710003' 的状态到日志表
log
[ 2025-08-27T16:13:01+08:00 ][ log ] 【业务触发】检查 '710003' 的状态变化...
log
[ 2025-08-27T16:13:01+08:00 ][ log ] 打印设备信息
log
[ 2025-08-27T16:13:01+08:00 ][ log ] array (
  'id' => 4,
  'sn' => '710003',
  'mainname' => '设备710003',
  'agent_id' => 82,
  'hospitals_id' => 48,
  'is_online' => 1,
  'lock_status' => 0,
  'car_status' => 0,
  'voltage' => '12.30',
  'signal_strength' => 24,
  'error_code' => 0,
  'admin_status' => 1,
  'use_status' => 1,
  'last_online_time' => 1755307616,
  'last_status_updatetime' => 1756281781,
  'createtime' => 1754897273,
  'updatetime' => 1756281781,
  'hardware_type' => 1,
  'departments_id' => 34,
  'platform_id' => 20,
  'notes' => '700003',
  'qrcode' => NULL,
  'kehu_id' => 1,
  'lat' => '0.0000',
  'lng' => '0.0000',
  'address' => '',
)
log
[ 2025-08-27T16:13:01+08:00 ][ log ] 打印四轮地锁有车无车的状态
log
[ 2025-08-27T16:13:01+08:00 ][ log ] 0
log
